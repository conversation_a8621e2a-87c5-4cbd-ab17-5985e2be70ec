"""
TravelPlannerAgent LangGraph实现

基于LangGraph重构的旅行规划Agent，替换原有的autogen实现。
专注于全自动模式实现，支持：
- 完全自动化的旅行规划流程
- 双模运行（精准续航规划 vs 通用驾驶辅助）
- 状态管理和SSE事件流
- 交互模式预留钩子
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import asyncio

from .travel_planner_langgraph import TravelPlannerGraph, create_initial_state, UserProfile, VehicleInfo
from .services import UserProfileService, MemoryService
from .travel_planner_langgraph.notification_service import NotificationService
from src.database.redis_client import get_redis_client


logger = logging.getLogger(__name__)


class TravelPlannerAgentLangGraph:
    """
    基于LangGraph的旅行规划Agent

    专注于全自动模式实现，提供完整的旅行规划服务：
    - 全自动规划：无需用户交互，直接生成完整方案
    - 流式规划：实时进度更新，适合前端展示
    - 双模运行：支持精准续航规划和通用驾驶辅助
    - 交互预留：为未来交互模式预留接口
    """

    def __init__(self, enable_interaction_hooks: bool = False):
        """
        初始化Agent

        Args:
            enable_interaction_hooks: 是否启用交互模式钩子（预留功能）
        """
        self.graph = TravelPlannerGraph(enable_interaction_hooks=enable_interaction_hooks)
        self.user_profile_service = UserProfileService()
        self.memory_service = MemoryService()
        self.enable_interaction_hooks = enable_interaction_hooks

    def _convert_final_state_to_response(self, final_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        V2架构: 格式化最终状态为API响应

        替代旧的SSEStreamAdapter中的部分功能。
        """
        if not final_state:
            return {"error": "Final state is empty."}
        
        return {
            "session_id": final_state.get("session_id"),
            "user_id": final_state.get("user_id"),
            "status": "completed" if not final_state.get("has_error") else "failed",
            "error_message": final_state.get("error_message"),
            "itinerary": final_state.get("final_itinerary"),
            "analysis": {
                "core_intent": final_state.get("core_intent"),
                "travel_preferences": final_state.get("travel_preferences"),
                "driving_context": final_state.get("driving_context"),
                "multi_city_strategy": final_state.get("multi_city_strategy"),
            },
            "metadata": {
                 "processing_time_seconds": final_state.get("processing_time_seconds"),
                 "tokens_used": final_state.get("tokens_used", 0),
                 "cost_estimate": final_state.get("cost_estimate", 0),
            }
        }

    async def plan_travel_automatic(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        全自动规划旅行（非流式）

        这是主要的规划方法，完全自动化处理用户请求，
        无需任何用户交互，直接生成完整的旅行方案。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）

        Returns:
            完整的规划结果
        """
        try:
            logger.info(f"开始旅行规划 - 用户: {user_id}")
            
            start_time = datetime.now()
            
            # 获取用户画像（如果未提供）
            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)
            
            # 运行全自动工作流
            final_state = await self.graph.run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            final_state["processing_time_seconds"] = processing_time
            
            # 保存用户记忆
            await self._save_planning_memory(user_id, query, final_state)
            
            # 转换为API响应格式
            response = self._convert_final_state_to_response(final_state)
            
            logger.info(f"旅行规划完成 - 用户: {user_id}, 耗时: {processing_time:.2f}秒")
            return response
            
        except Exception as e:
            logger.error(f"旅行规划失败 - 用户: {user_id}, 错误: {str(e)}")
            raise

    # ==================== 向后兼容方法 ====================

    async def plan_travel(self, *args, **kwargs) -> Dict[str, Any]:
        """规划旅行（默认全自动模式，保持向后兼容）"""
        return await self.plan_travel_automatic(*args, **kwargs)

    async def plan_travel_stream(self, *args, **kwargs) -> AsyncGenerator[str, None]:
        """
        流式规划旅行（V2兼容代理）
        
        警告：此方法已废弃。真正的V2流应由API层直接发起。
        这里仅为测试和兼容性保留，它会模拟API层的行为。
        """
        logger.warning("plan_travel_stream is deprecated. The V2 architecture should be initiated from the API layer.")
        session_id = kwargs.get("session_id") or f"session_{int(time.time())}"
        
        # 模拟API层行为
        redis_client = await get_redis_client()
        notification_service = NotificationService(redis_client=redis_client, session_id=session_id)
        
        # 启动后台任务
        asyncio.create_task(self.run_planning_with_notifications(
            notification_service=notification_service,
            **kwargs
        ))
        
        # 监听Redis并生成SSE
        async for event in self._redis_event_generator(notification_service):
            yield event

    # ==================== 交互模式预留方法 ====================
    # V1 `plan_travel_interactive` 和 `plan_travel_stream_interactive` 已被移除，
    # V2架构使用 `run_analysis_with_notifications` 和 `run_planning_with_notifications`
    # 并由API层直接调用。
    # =========================================================

    async def start_planning_phase(
        self,
        session_id: str,
        analysis_result: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """
        启动规划阶段 - 基于分析结果开始详细规划

        Args:
            session_id: 会话ID
            analysis_result: 分析阶段的结果

        Yields:
            str: SSE格式的事件流 (通过Redis)
        """
        logger.info(f"启动规划阶段 - Session: {session_id}")
        logger.debug(f"分析结果: {analysis_result}")

        # 模拟API层行为
        redis_client = await get_redis_client()
        notification_service = NotificationService(redis_client=redis_client, session_id=session_id)

        # 准备启动规划的数据
        user_id = analysis_result.get("user_id", "unknown")
        query = analysis_result.get("original_query", "") # 假设分析结果中有原始查询
        user_profile = analysis_result.get("user_profile")
        vehicle_info = analysis_result.get("vehicle_info")

        # 启动后台任务
        asyncio.create_task(self.run_planning_with_notifications(
            user_id=user_id,
            query=query,
            session_id=session_id,
            notification_service=notification_service,
            user_profile=user_profile,
            vehicle_info=vehicle_info,
            initial_state=analysis_result # 传递分析结果作为初始状态
        ))

        # 监听Redis
        async for event in self._redis_event_generator(notification_service):
            yield event

    async def get_planning_status(self, session_id: str) -> Dict[str, Any]:
        """
        获取规划状态
        
        Args:
            session_id: 会话ID
            
        Returns:
            规划状态信息
        """
        try:
            logger.info(f"获取规划状态 - Session: {session_id}")
            
            # 获取状态历史
            state_history = await self.graph.get_state_history(session_id)
            
            if not state_history:
                return {
                    "session_id": session_id,
                    "status": "not_found",
                    "message": "未找到对应的规划会话"
                }
            
            # 获取最新状态
            latest_state = state_history[0].values
            
            # 转换为状态响应
            response = {
                "session_id": session_id,
                "status": "completed" if latest_state.get("is_completed") else "processing",
                "current_stage": latest_state.get("current_stage"),
                "has_error": latest_state.get("has_error", False),
                "error_message": latest_state.get("error_message"),
                "progress": self._calculate_progress(latest_state.get("current_stage")),
                "created_at": latest_state.get("created_at"),
                "updated_at": latest_state.get("updated_at")
            }
            
            # 如果已完成，添加结果
            if latest_state.get("is_completed") and not latest_state.get("has_error"):
                response["result"] = self._convert_final_state_to_response(latest_state)
            
            return response
            
        except Exception as e:
            logger.error(f"获取规划状态失败 - Session: {session_id}, 错误: {str(e)}")
            return {
                "session_id": session_id,
                "status": "error",
                "error": str(e)
            }
    
    async def cancel_planning(self, session_id: str) -> Dict[str, Any]:
        """
        取消规划
        
        Args:
            session_id: 会话ID
            
        Returns:
            取消结果
        """
        try:
            logger.info(f"取消规划 - Session: {session_id}")
            
            # 这里可以实现取消逻辑
            # LangGraph目前没有直接的取消机制，可以通过状态标记实现
            
            return {
                "session_id": session_id,
                "status": "cancelled",
                "message": "规划已取消"
            }
            
        except Exception as e:
            logger.error(f"取消规划失败 - Session: {session_id}, 错误: {str(e)}")
            return {
                "session_id": session_id,
                "status": "error",
                "error": str(e)
            }
    
    def get_graph_visualization(self, include_interaction_hooks: bool = False) -> str:
        """
        获取工作流图可视化

        Args:
            include_interaction_hooks: 是否包含交互模式钩子（预留功能）

        Returns:
            Mermaid格式的图形定义
        """
        return self.graph.get_graph_visualization(include_interaction_hooks=include_interaction_hooks)

    def get_automatic_mode_info(self) -> Dict[str, Any]:
        """
        获取全自动模式信息

        Returns:
            全自动模式的详细信息
        """
        return {
            "mode": "automatic",
            "description": "完全自动化的旅行规划模式",
            "features": [
                "无需用户交互",
                "完整的规划流程",
                "双模运行支持（精准续航规划 vs 通用驾驶辅助）",
                "实时进度更新",
                "状态管理和恢复",
                "SSE事件流支持"
            ],
            "workflow_stages": [
                "核心意图分析",
                "多城市策略分析（如适用）",
                "驾驶情境分析（如适用）",
                "偏好分析",
                "行程生成",
                "行程优化"
            ],
            "interaction_hooks_enabled": self.enable_interaction_hooks,
            "interaction_hooks_available": [
                "用户确认节点（预留）",
                "用户反馈节点（预留）",
                "交互式规划方法（预留）",
                "交互式流式规划方法（预留）"
            ]
        }
    
    async def _save_planning_memory(
        self,
        user_id: str,
        query: str,
        final_state: Dict[str, Any]
    ):
        """保存规划记忆"""
        try:
            memory_content = {
                "original_query": query,
                "destinations": final_state.get("core_intent", {}).get("destinations", []),
                "days": final_state.get("core_intent", {}).get("days"),
                "planning_mode": final_state.get("planning_mode"),
                "success": not final_state.get("has_error", False),
                "session_id": final_state.get("session_id")
            }
            
            metadata = {
                "processing_time": final_state.get("processing_time_seconds", 0),
                "tokens_used": final_state.get("tokens_used", 0),
                "cost_estimate": final_state.get("cost_estimate", 0)
            }
            
            await self.memory_service.save_memory(
                user_id=user_id,
                memory_type="travel_planning",
                content=memory_content,
                metadata=metadata,
                importance=8 if not final_state.get("has_error") else 3,
                tags=["travel_planning", "langgraph"]
            )
            
            logger.info(f"规划记忆保存成功 - 用户: {user_id}")
            
        except Exception as e:
            logger.warning(f"保存规划记忆失败 - 用户: {user_id}, 错误: {str(e)}")
    
    def _calculate_progress(self, current_stage: Optional[str]) -> int:
        """根据当前阶段计算进度百分比"""
        stage_progress = {
            "intent_analysis": 20,
            "multi_city_strategy": 40,
            "driving_context": 50,
            "preference_analysis": 70,
            "itinerary_generation": 90,
            "optimization": 95,
            "completed": 100,
            "error": 0
        }
        
        return stage_progress.get(current_stage, 0)

    # ==================== V2推送架构方法 ====================

    async def _redis_event_generator(self, notification_service: NotificationService) -> AsyncGenerator[str, None]:
        """V2: 监听Redis并生成SSE事件的核心逻辑"""
        channel = notification_service.channel
        pubsub = notification_service.redis_client.pubsub()
        await pubsub.subscribe(channel)
        
        try:
            while True:
                message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=120)
                if message:
                    data = message['data'].decode('utf-8')
                    event_data = json.loads(data)
                    
                    yield f"data: {data}\n\n"

                    if event_data.get("event") == "eos":
                        break
                else:
                    logger.warning(f"Redis listener timed out for session {notification_service.session_id}")
                    break
                await asyncio.sleep(0.01)
        finally:
            await pubsub.unsubscribe(channel)

    async def run_analysis_with_notifications(
        self,
        user_id: str,
        query: str,
        session_id: str,
        notification_service: NotificationService,
        user_profile: Optional[UserProfile] = None,
        vehicle_info: Optional[VehicleInfo] = None
    ):
        """
        V2架构：带通知的分析阶段执行

        通过NotificationService发布事件，完全解耦推送逻辑。
        此方法由API层在后台任务中调用。
        """
        try:
            logger.info(f"V2架构分析阶段开始 - Session: {session_id}")

            config = { "configurable": { "notification_service": notification_service, "session_id": session_id } }

            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)

            analysis_stream = self.graph.stream_run_analysis_only(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id,
                config=config
            )

            final_node = ""
            final_state = None
            last_valid_state = None

            async for chunk in analysis_stream:
                if chunk:
                    final_node = list(chunk.keys())[0]
                    chunk_state = list(chunk.values())[0]

                    # 如果不是完成节点，保存状态
                    if final_node != "analysis_complete":
                        last_valid_state = chunk_state

                    final_state = chunk_state

            logger.info(f"V2分析流结束于节点: {final_node} - Session: {session_id}")

            # 如果最终状态是完成节点但没有完整数据，使用最后一个有效状态
            if final_node == "analysis_complete" and last_valid_state:
                logger.info(f"使用最后一个有效状态替代完成节点状态 - Session: {session_id}")
                final_state = last_valid_state

            # 检查是否有最终状态
            if not final_state:
                logger.warning(f"未从流中获取到最终状态，尝试从checkpointer获取 - Session: {session_id}")
                try:
                    state_history = await self.graph.get_state_history(session_id)
                    if state_history:
                        final_state = state_history[0]
                        logger.info(f"从checkpointer成功获取状态 - Session: {session_id}")
                    else:
                        logger.warning(f"checkpointer中也没有状态，使用默认完成状态 - Session: {session_id}")
                        final_state = {"session_id": session_id, "is_completed": True}
                except Exception as e:
                    logger.warning(f"从checkpointer获取状态失败: {str(e)} - Session: {session_id}")
                    final_state = {"session_id": session_id, "is_completed": True}

            # 确保final_state是字典格式
            if hasattr(final_state, 'values'):
                state_data = final_state.values
            else:
                state_data = final_state

            await notification_service.notify_final_result({
                "phase": "analysis",
                "session_id": session_id,
                "analysis_result": self._convert_final_state_to_response(state_data),
                "message": "分析阶段完成"
            })
            logger.info(f"V2架构分析阶段完成 - Session: {session_id}")

        except Exception as e:
            logger.error(f"V2架构分析阶段失败 - Session: {session_id}: {str(e)}", exc_info=True)
            await notification_service.notify_error(
                error_message=f"分析阶段失败: {str(e)}",
                step_name="analysis_phase"
            )

    async def run_planning_with_notifications(
        self,
        user_id: str,
        query: str,
        session_id: str,
        notification_service: NotificationService,
        user_profile: Optional[UserProfile] = None,
        vehicle_info: Optional[VehicleInfo] = None,
        initial_state: Optional[Dict[str, Any]] = None
    ):
        """
        V2架构：带通知的规划阶段执行

        通过NotificationService发布事件，完全解耦推送逻辑。
        """
        try:
            logger.info(f"V2架构规划阶段开始 - Session: {session_id}")
            
            config = { "configurable": { "notification_service": notification_service, "session_id": session_id } }

            # 如果没有提供初始状态，则从头运行
            if initial_state:
                graph_input = initial_state
            else:
                if not user_profile:
                    user_profile = await self.user_profile_service.get_user_profile(user_id)
                graph_input = {
                    "user_id": user_id,
                    "original_query": query,
                    "user_profile": user_profile,
                    "vehicle_info": vehicle_info,
                    "session_id": session_id
                }

            planning_stream = self.graph.stream_run_automatic(
                graph_input,
                config=config,
            )

            final_node = ""
            final_state = None
            async for chunk in planning_stream:
                if chunk:
                    final_node = list(chunk.keys())[0]
                    final_state = list(chunk.values())[0]

            # 检查是否有最终状态
            if not final_state:
                logger.warning(f"未从规划流中获取到最终状态，尝试从checkpointer获取 - Session: {session_id}")
                try:
                    state_history = await self.graph.get_state_history(session_id)
                    if state_history:
                        final_state = state_history[0]
                        logger.info(f"从checkpointer成功获取规划状态 - Session: {session_id}")
                    else:
                        logger.warning(f"checkpointer中也没有规划状态，使用默认完成状态 - Session: {session_id}")
                        final_state = {"session_id": session_id, "is_completed": True}
                except Exception as e:
                    logger.warning(f"从checkpointer获取规划状态失败: {str(e)} - Session: {session_id}")
                    final_state = {"session_id": session_id, "is_completed": True}

            # 确保final_state是字典格式
            if hasattr(final_state, 'values'):
                state_data = final_state.values
            else:
                state_data = final_state

            await self._save_planning_memory(user_id, query, state_data)

            await notification_service.notify_final_result({
                "phase": "planning",
                "session_id": session_id,
                "planning_result": self._convert_final_state_to_response(state_data),
                "message": "规划阶段完成"
            })

            logger.info(f"V2架构规划阶段完成 - Session: {session_id}")

        except Exception as e:
            logger.error(f"V2架构规划阶段失败 - Session: {session_id}: {str(e)}", exc_info=True)
            await notification_service.notify_error(
                error_message=f"规划阶段失败: {str(e)}",
                step_name="planning_phase"
            )

    async def run_full_planning_with_notifications(
        self,
        user_id: str,
        query: str,
        session_id: str,
        notification_service: NotificationService,
        user_profile: Optional[UserProfile] = None,
        vehicle_info: Optional[VehicleInfo] = None
    ):
        """
        V2架构：运行完整的分析+规划流程，并通过NotificationService推送进度

        这是一个统一的方法，会依次执行分析和规划两个阶段。
        """
        try:
            logger.info(f"V2架构完整流程开始 - Session: {session_id}")

            # 先执行分析阶段
            await self.run_analysis_with_notifications(
                user_id=user_id,
                query=query,
                session_id=session_id,
                notification_service=notification_service,
                user_profile=user_profile,
                vehicle_info=vehicle_info
            )

            # 然后执行规划阶段
            await self.run_planning_with_notifications(
                user_id=user_id,
                query=query,
                session_id=session_id,
                notification_service=notification_service,
                user_profile=user_profile,
                vehicle_info=vehicle_info
            )

            logger.info(f"V2架构完整流程完成 - Session: {session_id}")

        except Exception as e:
            logger.error(f"V2架构完整流程失败 - Session: {session_id}, Error: {str(e)}")
            await notification_service.notify_error(
                error_message=f"完整流程失败: {str(e)}",
                step_name="full_planning"
            )
