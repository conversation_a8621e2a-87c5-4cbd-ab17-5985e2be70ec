"""
旅行规划API接口

提供旅行规划的RESTful API和SSE流式接口，支持实时进度反馈。
"""
import asyncio
import json
import uuid
import time
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request, BackgroundTasks
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, StreamEvent, EventType, UserProfile
)
from src.models.mysql_crud import (
    ai_planning_session_crud
)
# 使用LangGraph重构的Agent
from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
from src.database.mongodb_client import get_mongo_client
from src.database.mysql_client import get_db
from src.database.redis_client import RedisClient, get_redis_client
from src.agents.travel_planner_langgraph.notification_service import NotificationService
from src.core.logger import get_logger

logger = get_logger("travel_planner_api")
router = APIRouter(prefix="/api/travel", tags=["旅行规划 (V2)"])


# ==============================================================================
# V2 Models
# ==============================================================================

class V2AnalysisRequest(BaseModel):
    user_id: str
    query: str
    session_id: Optional[str] = None

class V2PlanningRequest(BaseModel):
    user_id: str
    session_id: str
    query: str
    analysis_result: Dict[str, Any]

class V2ApiResponse(BaseModel):
    session_id: str
    status: str
    message: str

# ==============================================================================
# V2 Core Functions
# ==============================================================================

async def redis_event_generator(session_id: str, redis_client: RedisClient):
    """V2核心: 监听Redis频道并生成SSE事件"""
    channel = f"task:{session_id}"
    pubsub = redis_client.pubsub()
    await pubsub.subscribe(channel)
    
    try:
        while True:
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=180) # 3分钟超时
            if message:
                # 修复: message['data'] 已经是字符串，无需解码
                data = message['data']
                event_data = json.loads(data)
                
                yield f"data: {data}\n\n"
                
                if event_data.get("event") == "eos": # 检查结束信号
                    logger.info(f"收到流结束信号 (eos) - Session: {session_id}")
                    break
            else:
                # 超时后发送一个标准的ping事件，而不是注释
                logger.debug(f"Redis Pub/Sub 监听超时，发送ping事件 - Session: {session_id}")
                yield f"event: ping\ndata: {json.dumps({'time': time.time()})}\n\n"

    except asyncio.CancelledError:
        logger.info(f"客户端断开连接，取消监听 - Session: {session_id}")
    finally:
        await pubsub.unsubscribe(channel)
        logger.info(f"已取消对频道 {channel} 的订阅")

# ==============================================================================
# V2 API Endpoints
# ==============================================================================

@router.post("/v2/analysis/stream", summary="V2 - 启动分析阶段（流式）")
async def analysis_stream_v2(
    request: V2AnalysisRequest,
    background_tasks: BackgroundTasks,
    redis_client: RedisClient = Depends(get_redis_client)
):
    """
    V2 核心接口: 启动分析阶段，并通过SSE返回实时事件。
    
    此接口会立即返回一个SSE连接，同时在后台启动Agent的分析流程。
    Agent会将每一步的进展通过Redis发布，前端通过此SSE连接接收。
    """
    session_id = request.session_id or f"analysis_{request.user_id}_{int(time.time())}"
    
    agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=True)
    notification_service = NotificationService(redis_client, session_id)

    background_tasks.add_task(
        agent.run_analysis_with_notifications,
        user_id=request.user_id,
        query=request.query,
        session_id=session_id,
        notification_service=notification_service
    )
    
    return EventSourceResponse(redis_event_generator(session_id, redis_client))

@router.post("/v2/planning/start", summary="V2 - 启动规划阶段（流式）", response_model=V2ApiResponse)
async def start_planning_v2(
    request: V2PlanningRequest,
    background_tasks: BackgroundTasks,
    redis_client: RedisClient = Depends(get_redis_client)
):
    """
    V2 核心接口: 基于分析结果启动完整的旅行规划流程。

    此接口用于前端在“分析阶段”完成后，用户点击“立即规划”时调用。
    """
    session_id = request.session_id
    agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=True)
    notification_service = NotificationService(redis_client, session_id)

    background_tasks.add_task(
        agent.run_planning_with_notifications,
        user_id=request.user_id,
        query=request.query,
        session_id=session_id,
        notification_service=notification_service,
        initial_state=request.analysis_result # 将分析结果作为初始状态传入
    )
    
    return V2ApiResponse(
        session_id=session_id,
        status="planning_started",
        message="规划阶段已在后台启动，请继续监听现有SSE连接获取更新。"
    )

# ==============================================================================
# V1 Deprecated Endpoints
# ==============================================================================

class PlanRequest(BaseModel):
    """(V1 Deprecated) 规划请求模型"""
    query: str
    user_id: str
    session_id: Optional[str] = None

class PlanResponse(BaseModel):
    """(V1 Deprecated) 规划响应模型"""
    trace_id: str
    status: str
    message: str


@router.post("/plan", response_model=PlanResponse, deprecated=True, summary="V1 (Deprecated) - 创建规划任务")
async def create_travel_plan_v1(request: PlanRequest):
    """
    **[V1 已废弃]** 请使用 `/v2/analysis/stream` 接口替代。
    """
    logger.warning("Deprecated V1 endpoint /plan was called.")
    session_id = request.session_id or f"analysis_{request.user_id}_{int(time.time())}"
    return PlanResponse(
        trace_id=session_id,
        status="deprecated",
        message="This endpoint is deprecated. Please use the V2 SSE endpoint."
    )

@router.get("/plan/{trace_id}/stream", deprecated=True, summary="V1 (Deprecated) - 流式获取规划")
async def stream_travel_plan_v1(trace_id: str, user_id: str, query: str):
    """
    **[V1 已废弃]** 此接口的逻辑已合并到 `/v2/analysis/stream`。
    
    为了向后兼容，此接口将直接重定向到V2的实现。
    """
    logger.warning(f"Deprecated V1 endpoint /plan/{trace_id}/stream was called. Redirecting to V2 logic.")
    
    async def event_generator():
        # 直接调用V2的核心逻辑
        agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=True)
        redis_client = await get_redis_client()
        notification_service = NotificationService(redis_client, trace_id)

        # 在后台运行
        asyncio.create_task(
            agent.run_analysis_with_notifications(
                user_id=user_id,
                query=query,
                session_id=trace_id,
                notification_service=notification_service
            )
        )
        
        # 监听Redis
        async for event in redis_event_generator(trace_id, redis_client):
            yield event

    return EventSourceResponse(event_generator())


@router.post("/plan/{trace_id}/start_planning", deprecated=True, summary="V1 (Deprecated) - 启动规划阶段")
async def start_planning_phase_v1(
    trace_id: str,
    analysis_result: Dict[str, Any]
):
    """
    **[V1 已废弃]** 请使用 `/v2/planning/start` 接口替代。
    """
    logger.warning(f"Deprecated V1 endpoint /plan/{trace_id}/start_planning was called.")
    
    # 假设 analysis_result 包含了 user_id 和 query
    user_id = analysis_result.get("user_id", "unknown_user")
    query = analysis_result.get("original_query", "")

    request = V2PlanningRequest(
        user_id=user_id,
        session_id=trace_id,
        query=query,
        analysis_result=analysis_result
    )
    
    # 手动获取依赖
    redis_client = await get_redis_client()
    
    # 创建一个临时的 BackgroundTasks 实例来调用
    background_tasks = BackgroundTasks()
    
    await start_planning_v2(request, background_tasks, redis_client)
    # 注意：这里不会执行后台任务，因为它们已经被添加但没有被FastAPI处理
    # 这是一个简化的兼容性处理，鼓励客户端迁移
    
    return {"message": "Request forwarded to V2 planning. Please listen on the original SSE stream."}

# ==============================================================================
# Other Endpoints (Kept for compatibility)
# ==============================================================================

@router.get("/plan/{trace_id}", summary="获取单个规划结果")
async def get_travel_plan(trace_id: str):
    """
    获取一个已完成的旅行规划的最终结果。
    """
    try:
        agent = TravelPlannerAgentLangGraph()
        status = await agent.get_planning_status(trace_id)
        if status.get("status") == "not_found":
            raise HTTPException(status_code=404, detail=status.get("message"))
        
        # 如果规划已完成且成功，返回最终结果
        if status.get("status") == "completed" and status.get("result"):
            return status["result"]
        
        # 如果仍在处理中或失败，返回当前状态
        return status

    except Exception as e:
        logger.error(f"获取规划结果失败: {trace_id}, {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取规划结果失败: {str(e)}")


@router.get("/user/{user_id}/plans", summary="获取用户的所有规划历史")
async def get_user_plans(
    user_id: str,
    limit: int = 20,
    skip: int = 0,
    status: Optional[str] = None
):
    """
    获取指定用户的所有旅行规划历史记录。
    """
    try:
        mongo_client = await get_mongo_client()
        plans = await mongo_client.get_itineraries_by_user(
            user_id=user_id,
            limit=limit,
            skip=skip,
            status=status
        )
        return plans
    except Exception as e:
        logger.error(f"获取用户规划历史失败: {user_id}, {str(e)}")
        raise HTTPException(status_code=500, detail="获取用户规划历史失败")


@router.put("/plan/{trace_id}", summary="更新一个规划")
async def update_travel_plan(trace_id: str, update_data: Dict[str, Any]):
    """
    更新一个现有的旅行规划。
    
    (此功能为预留，具体实现待定)
    """
    logger.info(f"收到更新规划请求: {trace_id}")
    return {"message": "功能待实现", "trace_id": trace_id}


@router.post("/feedback", summary="提交用户反馈")
async def submit_feedback(feedback_data: Dict[str, Any]):
    """
    接收用户对规划结果的反馈。
    """
    try:
        mongo_client = await get_mongo_client()
        await mongo_client.log_analytics({
            "event_type": "user_feedback",
            "properties": feedback_data,
            "timestamp": datetime.now().isoformat()
        })
        logger.info(f"收到用户反馈: {feedback_data.get('session_id')}")
        return {"status": "success", "message": "感谢您的反馈"}
    except Exception as e:
        logger.error(f"保存反馈失败: {str(e)}")
        raise HTTPException(status_code=500, detail="保存反馈失败")


@router.get("/health", summary="健康检查")
async def health_check():
    """
    API健康检查端点。
    """
    return {
        "status": "ok",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "healthy",
            # 未来可以添加对数据库和LLM服务的检查
        }
    }
